version: '3.8'

services:
  # ==================== 数据库服务 ====================
  
  # PostgreSQL 主数据库
  postgres:
    image: postgres:15-alpine
    container_name: ai-travel-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ai_travel_planner
      POSTGRES_USER: travel_user
      POSTGRES_PASSWORD: travel_password_2024
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - travel-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U travel_user -d ai_travel_planner"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存和会话存储
  redis:
    image: redis:7-alpine
    container_name: ai-travel-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password_2024
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - travel-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Qdrant 向量数据库
  qdrant:
    image: qdrant/qdrant:latest
    container_name: ai-travel-qdrant
    restart: unless-stopped
    volumes:
      - qdrant_data:/qdrant/storage
      - ./qdrant/config:/qdrant/config
    ports:
      - "6333:6333"
      - "6334:6334"
    networks:
      - travel-network
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
      QDRANT__LOG_LEVEL: INFO
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Elasticsearch (可选，用于全文搜索)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: ai-travel-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
      - "9300:9300"
    networks:
      - travel-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # ==================== 后端服务 ====================

  # API 网关
  api-gateway:
    build:
      context: .
      dockerfile: services/api-gateway/Dockerfile
    container_name: ai-travel-api-gateway
    restart: unless-stopped
    ports:
      - "8000:8000"
    networks:
      - travel-network
    environment:
      - NODE_ENV=production
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_password_2024
      - JWT_SECRET=your_jwt_secret_key_2024
      - LOG_LEVEL=info
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./logs:/app/logs

  # 聊天服务
  chat-service:
    build:
      context: .
      dockerfile: services/chat-service/Dockerfile
    container_name: ai-travel-chat-service
    restart: unless-stopped
    ports:
      - "8001:8001"
    networks:
      - travel-network
    environment:
      - DATABASE_URL=***********************************************************/ai_travel_planner
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_password_2024
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LOG_LEVEL=info
      - MAX_WORKERS=4
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./logs:/app/logs

  # RAG 服务
  rag-service:
    build:
      context: .
      dockerfile: services/rag-service/Dockerfile
    container_name: ai-travel-rag-service
    restart: unless-stopped
    ports:
      - "8002:8002"
    networks:
      - travel-network
    environment:
      - DATABASE_URL=***********************************************************/ai_travel_planner
      - QDRANT_HOST=qdrant
      - QDRANT_PORT=6333
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_password_2024
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LOG_LEVEL=info
    depends_on:
      postgres:
        condition: service_healthy
      qdrant:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./logs:/app/logs
      - ./data/documents:/app/documents

  # 智能体服务
  agent-service:
    build:
      context: .
      dockerfile: services/agent-service/Dockerfile
    container_name: ai-travel-agent-service
    restart: unless-stopped
    ports:
      - "8003:8003"
    networks:
      - travel-network
    environment:
      - DATABASE_URL=***********************************************************/ai_travel_planner
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_password_2024
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LOG_LEVEL=info
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./logs:/app/logs

  # 规划服务
  planning-service:
    build:
      context: .
      dockerfile: services/planning-service/Dockerfile
    container_name: ai-travel-planning-service
    restart: unless-stopped
    ports:
      - "8004:8004"
    networks:
      - travel-network
    environment:
      - DATABASE_URL=***********************************************************/ai_travel_planner
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_password_2024
      - LOG_LEVEL=info
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./logs:/app/logs

  # 集成服务
  integration-service:
    build:
      context: .
      dockerfile: services/integration-service/Dockerfile
    container_name: ai-travel-integration-service
    restart: unless-stopped
    ports:
      - "8005:8005"
    networks:
      - travel-network
    environment:
      - DATABASE_URL=***********************************************************/ai_travel_planner
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_password_2024
      - FLIGHT_API_KEY=${FLIGHT_API_KEY}
      - HOTEL_API_KEY=${HOTEL_API_KEY}
      - WEATHER_API_KEY=${WEATHER_API_KEY}
      - LOG_LEVEL=info
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8005/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./logs:/app/logs

  # 用户服务
  user-service:
    build:
      context: .
      dockerfile: services/user-service/Dockerfile
    container_name: ai-travel-user-service
    restart: unless-stopped
    ports:
      - "8006:8006"
    networks:
      - travel-network
    environment:
      - DATABASE_URL=***********************************************************/ai_travel_planner
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_password_2024
      - JWT_SECRET=your_jwt_secret_key_2024
      - LOG_LEVEL=info
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8006/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./logs:/app/logs

  # ==================== 前端服务 ====================

  # Web 前端
  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile
    container_name: ai-travel-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    networks:
      - travel-network
    environment:
      - REACT_APP_API_BASE_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000
      - NODE_ENV=production
    depends_on:
      - api-gateway
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ==================== 监控和日志 ====================

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: ai-travel-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    networks:
      - travel-network
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    container_name: ai-travel-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    networks:
      - travel-network
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus

  # Jaeger 分布式追踪
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: ai-travel-jaeger
    restart: unless-stopped
    ports:
      - "5775:5775/udp"
      - "6831:6831/udp"
      - "6832:6832/udp"
      - "5778:5778"
      - "16686:16686"
      - "14250:14250"
      - "14268:14268"
      - "14269:14269"
      - "9411:9411"
    networks:
      - travel-network
    environment:
      - COLLECTOR_OTLP_ENABLED=true

  # ELK Stack for logging
  elasticsearch-log:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: ai-travel-elasticsearch-log
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_log_data:/usr/share/elasticsearch/data
    ports:
      - "9201:9200"
    networks:
      - travel-network

  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: ai-travel-logstash
    restart: unless-stopped
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
      - ./logs:/usr/share/logstash/logs
    ports:
      - "5044:5044"
    networks:
      - travel-network
    depends_on:
      - elasticsearch-log

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: ai-travel-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    networks:
      - travel-network
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch-log:9200
    depends_on:
      - elasticsearch-log

  # ==================== 工具服务 ====================

  # Nginx 反向代理和负载均衡
  nginx:
    image: nginx:alpine
    container_name: ai-travel-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    networks:
      - travel-network
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/ssl/certs
      - nginx_logs:/var/log/nginx
    depends_on:
      - api-gateway
      - frontend

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    container_name: ai-travel-minio
    restart: unless-stopped
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - travel-network
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin123
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Adminer 数据库管理
  adminer:
    image: adminer:latest
    container_name: ai-travel-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    networks:
      - travel-network
    depends_on:
      - postgres

  # Redis Commander
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: ai-travel-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    networks:
      - travel-network
    environment:
      - REDIS_HOSTS=local:redis:6379
      - REDIS_PASSWORD=redis_password_2024
    depends_on:
      - redis

  # ==================== 任务调度 ====================

  # Celery Worker (异步任务处理)
  celery-worker:
    build:
      context: .
      dockerfile: services/celery/Dockerfile
    container_name: ai-travel-celery-worker
    restart: unless-stopped
    networks:
      - travel-network
    environment:
      - DATABASE_URL=***********************************************************/ai_travel_planner
      - REDIS_URL=redis://:redis_password_2024@redis:6379/0
      - LOG_LEVEL=info
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
    command: celery -A tasks worker --loglevel=info --concurrency=4

  # Celery Beat (定时任务)
  celery-beat:
    build:
      context: .
      dockerfile: services/celery/Dockerfile
    container_name: ai-travel-celery-beat
    restart: unless-stopped
    networks:
      - travel-network
    environment:
      - DATABASE_URL=***********************************************************/ai_travel_planner
      - REDIS_URL=redis://:redis_password_2024@redis:6379/0
      - LOG_LEVEL=info
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
    command: celery -A tasks beat --loglevel=info

  # Flower (Celery 监控)
  flower:
    build:
      context: .
      dockerfile: services/celery/Dockerfile
    container_name: ai-travel-flower
    restart: unless-stopped
    ports:
      - "5555:5555"
    networks:
      - travel-network
    environment:
      - REDIS_URL=redis://:redis_password_2024@redis:6379/0
    depends_on:
      - redis
    command: celery -A tasks flower --port=5555

# ==================== 网络配置 ====================
networks:
  travel-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ==================== 数据卷配置 ====================
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  qdrant_data:
    driver: local
  elasticsearch_data:
    driver: local
  elasticsearch_log_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  minio_data:
    driver: local
  nginx_logs:
    driver: local 