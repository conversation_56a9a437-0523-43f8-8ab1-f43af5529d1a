{"name": "ai-travel-planner-frontend", "version": "1.0.0", "description": "AI智能旅行规划助手前端应用", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "test": "jest", "test:coverage": "jest --coverage"}, "dependencies": {"@ant-design/icons": "^5.0.1", "@reduxjs/toolkit": "^1.9.3", "@tanstack/react-query": "^4.24.6", "@types/mapbox-gl": "^2.7.10", "antd": "^5.2.2", "axios": "^1.3.4", "classnames": "^2.3.2", "dayjs": "^1.11.7", "framer-motion": "^10.2.4", "highlight.js": "^11.7.0", "lodash": "^4.17.21", "mapbox-gl": "^2.13.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-helmet-async": "^1.3.0", "react-infinite-scroll-component": "^6.1.0", "react-map-gl": "^7.0.21", "react-markdown": "^8.0.5", "react-redux": "^8.0.5", "react-router-dom": "^6.8.0", "recharts": "^2.5.0", "socket.io-client": "^4.6.1"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/lodash": "^4.14.191", "@types/node": "^18.14.6", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.14", "eslint": "^8.35.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "jest": "^29.4.3", "postcss": "^8.4.21", "prettier": "^2.8.4", "tailwindcss": "^3.2.7", "typescript": "^4.9.5", "vite": "^7.0.6"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}