version: '3.8'

services:
  # ==================== 数据库服务 ====================
  mysql-prod:
    image: mysql:8.0
    container_name: ai-travel-mysql-prod
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-SecureRootPass2024!}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-ai_travel_planner}
      MYSQL_USER: ${MYSQL_USER:-ai_travel_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-SecureUserPass2024!}
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    volumes:
      - mysql_data_prod:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d:ro
      - ./mysql/init:/docker-entrypoint-initdb.d:ro
    ports:
      - "3306:3306"
    command: >
      mysqld
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --default-authentication-plugin=mysql_native_password
      --innodb-buffer-pool-size=512M
      --innodb-log-file-size=256M
      --max-connections=500
      --slow-query-log=1
      --long-query-time=2
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
      interval: 30s
    networks:
      - ai-travel-network

  # ==================== 缓存服务 ====================
  redis-prod:
    image: redis:7-alpine
    container_name: ai-travel-redis-prod
    restart: unless-stopped
    command: >
      redis-server
      --appendonly yes
      --appendfsync everysec
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    volumes:
      - redis_data_prod:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - ai-travel-network

  # ==================== 向量数据库 ====================
  qdrant-prod:
    image: qdrant/qdrant:v1.14.1
    container_name: ai-travel-qdrant-prod
    restart: unless-stopped
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
      QDRANT__LOG_LEVEL: INFO
    volumes:
      - qdrant_data_prod:/qdrant/storage
      - ./qdrant/config.yaml:/qdrant/config/production.yaml:ro
    ports:
      - "6333:6333"
      - "6334:6334"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - ai-travel-network

  # ==================== 搜索引擎 ====================
  elasticsearch-prod:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.16.0
    container_name: ai-travel-elasticsearch-prod
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data_prod:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9200/_health"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - ai-travel-network

  # ==================== 核心服务 ====================
  api-gateway-prod:
    build:
      context: ../../
      dockerfile: services/api-gateway/Dockerfile.prod
    container_name: ai-travel-api-gateway-prod
    restart: unless-stopped
    environment:
      - ENV=production
      - DATABASE_URL=mysql+aiomysql://${MYSQL_USER:-ai_travel_user}:${MYSQL_PASSWORD:-SecureUserPass2024!}@mysql-prod:3306/${MYSQL_DATABASE:-ai_travel_planner}
      - REDIS_URL=redis://redis-prod:6379/0
      - QDRANT_URL=http://qdrant-prod:6333
      - ELASTICSEARCH_URL=http://elasticsearch-prod:9200
    volumes:
      - api_logs_prod:/app/logs
    ports:
      - "8080:8080"
    depends_on:
      mysql-prod:
        condition: service_healthy
      redis-prod:
        condition: service_healthy
      qdrant-prod:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - ai-travel-network

  chat-service-prod:
    build:
      context: ../../
      dockerfile: services/chat-service/Dockerfile.prod
    container_name: ai-travel-chat-service-prod
    restart: unless-stopped
    environment:
      - ENV=production
      - DATABASE_URL=mysql+aiomysql://${MYSQL_USER:-ai_travel_user}:${MYSQL_PASSWORD:-SecureUserPass2024!}@mysql-prod:3306/${MYSQL_DATABASE:-ai_travel_planner}
      - REDIS_URL=redis://redis-prod:6379/1
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - VLLM_URL=${VLLM_URL:-http://vllm-service-prod:8001}
    volumes:
      - chat_logs_prod:/app/logs
    ports:
      - "8001:8001"
    depends_on:
      mysql-prod:
        condition: service_healthy
      redis-prod:
        condition: service_healthy
      api-gateway-prod:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - ai-travel-network

  agent-service-prod:
    build:
      context: ../../
      dockerfile: services/agent-service/Dockerfile.prod
    container_name: ai-travel-agent-service-prod
    restart: unless-stopped
    environment:
      - ENV=production
      - DATABASE_URL=mysql+aiomysql://${MYSQL_USER:-ai_travel_user}:${MYSQL_PASSWORD:-SecureUserPass2024!}@mysql-prod:3306/${MYSQL_DATABASE:-ai_travel_planner}
      - REDIS_URL=redis://redis-prod:6379/2
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - agent_logs_prod:/app/logs
    ports:
      - "8002:8002"
    depends_on:
      mysql-prod:
        condition: service_healthy
      redis-prod:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - ai-travel-network

  rag-service-prod:
    build:
      context: ../../
      dockerfile: services/rag-service/Dockerfile.prod
    container_name: ai-travel-rag-service-prod
    restart: unless-stopped
    environment:
      - ENV=production
      - DATABASE_URL=mysql+aiomysql://${MYSQL_USER:-ai_travel_user}:${MYSQL_PASSWORD:-SecureUserPass2024!}@mysql-prod:3306/${MYSQL_DATABASE:-ai_travel_planner}
      - REDIS_URL=redis://redis-prod:6379/3
      - QDRANT_URL=http://qdrant-prod:6333
      - ELASTICSEARCH_URL=http://elasticsearch-prod:9200
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - rag_logs_prod:/app/logs
    ports:
      - "8003:8003"
    depends_on:
      mysql-prod:
        condition: service_healthy
      redis-prod:
        condition: service_healthy
      qdrant-prod:
        condition: service_healthy
      elasticsearch-prod:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - ai-travel-network

  user-service-prod:
    build:
      context: ../../
      dockerfile: services/user-service/Dockerfile.prod
    container_name: ai-travel-user-service-prod
    restart: unless-stopped
    environment:
      - ENV=production
      - DATABASE_URL=mysql+aiomysql://${MYSQL_USER:-ai_travel_user}:${MYSQL_PASSWORD:-SecureUserPass2024!}@mysql-prod:3306/${MYSQL_DATABASE:-ai_travel_planner}
      - REDIS_URL=redis://redis-prod:6379/4
      - JWT_SECRET=${JWT_SECRET}
    volumes:
      - user_logs_prod:/app/logs
    ports:
      - "8004:8004"
    depends_on:
      mysql-prod:
        condition: service_healthy
      redis-prod:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - ai-travel-network

  # ==================== 工作流服务 ====================
  n8n-prod:
    image: n8nio/n8n:1.101.1
    container_name: ai-travel-n8n-prod
    restart: unless-stopped
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=${N8N_BASIC_AUTH_USER:-admin}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_BASIC_AUTH_PASSWORD:-SecureN8nPass2024!}
      - N8N_HOST=${N8N_HOST:-localhost}
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=http://${N8N_HOST:-localhost}:5678/
      - GENERIC_TIMEZONE=Asia/Shanghai
      - DB_TYPE=mysqldb
      - DB_MYSQLDB_HOST=mysql-prod
      - DB_MYSQLDB_PORT=3306
      - DB_MYSQLDB_DATABASE=${MYSQL_DATABASE:-ai_travel_planner}
      - DB_MYSQLDB_USER=${MYSQL_USER:-ai_travel_user}
      - DB_MYSQLDB_PASSWORD=${MYSQL_PASSWORD:-SecureUserPass2024!}
    volumes:
      - n8n_data_prod:/home/<USER>/.n8n
    ports:
      - "5678:5678"
    depends_on:
      mysql-prod:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - ai-travel-network

  # ==================== 监控服务 ====================
  prometheus-prod:
    image: prom/prometheus:v2.55.1
    container_name: ai-travel-prometheus-prod
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data_prod:/prometheus
    ports:
      - "9090:9090"
    networks:
      - ai-travel-network

  grafana-prod:
    image: grafana/grafana:11.4.0
    container_name: ai-travel-grafana-prod
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-SecureGrafanaPass2024!}
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data_prod:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
    ports:
      - "3000:3000"
    depends_on:
      - prometheus-prod
    networks:
      - ai-travel-network

  # ==================== 日志收集 ====================
  filebeat-prod:
    image: docker.elastic.co/beats/filebeat:8.16.0
    container_name: ai-travel-filebeat-prod
    restart: unless-stopped
    user: root
    volumes:
      - ./filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - api_logs_prod:/var/log/api:ro
      - chat_logs_prod:/var/log/chat:ro
      - agent_logs_prod:/var/log/agent:ro
      - rag_logs_prod:/var/log/rag:ro
      - user_logs_prod:/var/log/user:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - ELASTICSEARCH_HOSTS=elasticsearch-prod:9200
    depends_on:
      elasticsearch-prod:
        condition: service_healthy
    networks:
      - ai-travel-network

  # ==================== 负载均衡 ====================
  nginx-prod:
    image: nginx:1.25-alpine
    container_name: ai-travel-nginx-prod
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs_prod:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - api-gateway-prod
      - chat-service-prod
      - agent-service-prod
      - rag-service-prod
      - user-service-prod
      - n8n-prod
      - grafana-prod
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - ai-travel-network

# ==================== 网络配置 ====================
networks:
  ai-travel-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ==================== 数据卷 ====================
volumes:
  mysql_data_prod:
    driver: local
  redis_data_prod:
    driver: local
  qdrant_data_prod:
    driver: local
  elasticsearch_data_prod:
    driver: local
  n8n_data_prod:
    driver: local
  prometheus_data_prod:
    driver: local
  grafana_data_prod:
    driver: local
  api_logs_prod:
    driver: local
  chat_logs_prod:
    driver: local
  agent_logs_prod:
    driver: local
  rag_logs_prod:
    driver: local
  user_logs_prod:
    driver: local
  nginx_logs_prod:
    driver: local 