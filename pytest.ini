[tool:pytest]
# 测试发现
testpaths = services
python_files = test_*.py *_test.py
python_classes = Test* *Test
python_functions = test_*

# 输出选项
addopts = 
    -v
    --strict-markers
    --strict-config
    --tb=short
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80

# 标记
markers =
    unit: 单元测试
    integration: 集成测试
    e2e: 端到端测试
    slow: 慢速测试
    smoke: 冒烟测试
    api: API测试
    database: 数据库测试
    redis: Redis测试
    ai: AI模型测试

# 过滤器
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning

# 异步支持
asyncio_mode = auto

# 最小版本
minversion = 6.0 